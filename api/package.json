{"name": "social-network-api", "version": "1.0.0", "scripts": {"start": "node dist/server.js", "build": "tsc", "dev": "nodemon --ext ts --legacy-watch --polling-interval 1000 src/server.ts"}, "dependencies": {"express": "^4.18.2", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "pg": "^8.16.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "cors": "^2.8.5", "winston": "^3.10.0", "express-rate-limit": "^7.1.0", "dotenv": "^16.5.0"}, "devDependencies": {"typescript": "^5.2.2", "ts-node": "^10.9.1", "@types/express": "^4.17.17", "@types/node": "^20.5.9", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/cors": "^2.8.13", "@types/pg": "^8.15.4", "nodemon": "^3.0.1"}}