import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { AppError } from "../types";
import { jwtConfig } from "../config/env";

export const authMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const token = req.headers.authorization?.split(" ")[1];
  if (!token) {
    throw new AppError("No token provided", 401);
  }

  try {
    const decoded = jwt.verify(token, jwtConfig.secret) as {
      userId: number;
    };
    req.user = { id: decoded.userId };
    next();
  } catch (error) {
    throw new AppError("Invalid token", 401);
  }
};
