import { AppDataSource } from "./config/database";
import app from "./app";
import logger from "./config/logger";
import { appConfig } from "./config/env";

const PORT = appConfig.port;
AppDataSource.initialize()
  .then(() => {
    app.listen(PORT, () => {
      logger.info(`Server is running on port ${PORT}`);
    });
  })
  .catch((error) => {
    logger.error("Database connection error:", error);
    process.exit(1);
  });
