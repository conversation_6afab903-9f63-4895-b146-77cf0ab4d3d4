import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("users")
export class User {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ length: 50 })
  username!: string;

  @Column({ length: 100, unique: true })
  login!: string;

  @Column({ length: 255 })
  password!: string;

  @Column({ type: "enum", enum: ["user", "admin"], default: "user" })
  role!: "user" | "admin";

  @CreateDateColumn()
  created_at!: Date;

  @UpdateDateColumn()
  updated_at!: Date;
}
