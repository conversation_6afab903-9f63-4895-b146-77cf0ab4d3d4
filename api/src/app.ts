import express, { Request, Response, NextFunction } from "express";
import helmet from "helmet";
import cors from "cors";
import rateLimit from "express-rate-limit";
import { errorMiddleware } from "./middleware/errorMiddleware";
import authRoutes from "./routes/authRoutes";
import chatRoutes from "./routes/chatRoutes";
import messageRoutes from "./routes/messageRoutes";
import relationshipRoutes from "./routes/relationshipRoutes";
import logger from "./config/logger";
import { AppError } from "./types";
import { securityConfig, appConfig } from "./config/env";

const app = express();

app.use(helmet());
app.use(cors({
  origin: securityConfig.corsOrigin === "*" ? true : securityConfig.corsOrigin.split(","),
  credentials: true,
}));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

app.use(
  rateLimit({
    windowMs: securityConfig.rateLimitWindowMs,
    max: securityConfig.rateLimitMaxRequests,
    message: {
      error: "Too many requests from this IP, please try again later.",
    },
    standardHeaders: true,
    legacyHeaders: false,
  })
);

app.use((req, res, next) => {
  logger.info(`${req.method} ${req.originalUrl} - ${req.ip}`);
  next();
});

// Health check endpoint
app.get("/health", (req: Request, res: Response) => {
  res.status(200).json({
    status: "ok",
    timestamp: new Date().toISOString(),
    app: appConfig.name,
    version: appConfig.version,
    environment: appConfig.nodeEnv,
  });
});

// App info endpoint
app.get("/info", (req: Request, res: Response) => {
  res.status(200).json({
    name: appConfig.name,
    version: appConfig.version,
    environment: appConfig.nodeEnv,
    timestamp: new Date().toISOString(),
  });
});

app.use("/api/auth", authRoutes);
app.use("/api/chats", chatRoutes);
app.use("/api/messages", messageRoutes);
// app.use(
//   "/api/messages",
//   (req: Request, res: Response, next: NextFunction) => {
//     console.log(`Entering /api/messages: ${req.method} ${req.originalUrl}`);
//     next();
//   },
//   messageRoutes
// );
app.use("/api/relationships", relationshipRoutes);

app.use((req: Request, res: Response, next: NextFunction) => {
  next(new AppError(`Route ${req.originalUrl} not found`, 404));
});
app.use(errorMiddleware);

export default app;
