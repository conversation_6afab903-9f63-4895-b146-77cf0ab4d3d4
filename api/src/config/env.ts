import dotenv from "dotenv";
import path from "path";

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === "production" 
  ? ".env" 
  : process.env.NODE_ENV === "test" 
    ? ".env.test" 
    : ".env.development";

// Load the appropriate .env file
dotenv.config({ path: path.resolve(process.cwd(), envFile) });

// Also load .env as fallback
dotenv.config();

// Environment configuration with validation and defaults
export const config = {
  // Application settings
  app: {
    name: process.env.APP_NAME || "STBackend API",
    version: process.env.APP_VERSION || "1.0.0",
    port: parseInt(process.env.PORT || "5555", 10),
    nodeEnv: process.env.NODE_ENV || "development",
    logLevel: process.env.LOG_LEVEL || "info",
  },

  // Database configuration
  database: {
    host: process.env.MYSQL_HOST || "localhost",
    port: parseInt(process.env.MYSQL_PORT || "3306", 10),
    username: process.env.MYSQL_USER || "root",
    password: process.env.MYSQL_PASSWORD || "",
    database: process.env.DATABASE_NAME || "simple_telegram",
    type: (process.env.DATABASE_TYPE || "mysql") as "mysql" | "postgres",
  },

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || "fallback-secret-for-development",
    expiresIn: process.env.JWT_EXPIRES_IN || "1h",
  },

  // Security settings
  security: {
    corsOrigin: process.env.CORS_ORIGIN || "*",
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000", 10), // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100", 10),
  },

  // Feature flags
  features: {
    enableSwagger: process.env.ENABLE_SWAGGER === "true" || process.env.NODE_ENV !== "production",
    enableDebugLogging: process.env.DEBUG === "true" || process.env.NODE_ENV === "development",
  },
};

// Validation function
export const validateConfig = (): void => {
  const requiredEnvVars = [
    "MYSQL_PASSWORD",
    "JWT_SECRET",
    "DATABASE_NAME",
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(", ")}\n` +
      `Please check your .env file or environment configuration.`
    );
  }

  // Validate JWT secret strength in production
  if (config.app.nodeEnv === "production" && config.jwt.secret.length < 32) {
    throw new Error("JWT_SECRET must be at least 32 characters long in production");
  }

  // Validate database password in production
  if (config.app.nodeEnv === "production" && config.database.password.length < 8) {
    throw new Error("MYSQL_PASSWORD must be at least 8 characters long in production");
  }
};

// Export individual config sections for convenience
export const {
  app: appConfig,
  database: dbConfig,
  jwt: jwtConfig,
  security: securityConfig,
  features: featureConfig,
} = config;

// Validate configuration on import
if (process.env.NODE_ENV !== "test") {
  validateConfig();
}
