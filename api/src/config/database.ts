import { DataSource } from "typeorm";
import { User } from "../models/User";
import { Chat } from "../models/Chat";
import { ChatMember } from "../models/ChatMember";
import { Message } from "../models/Message";
import { UserRelationship } from "../models/UserRelationship";
import { MessageStatus } from "../models/MessageStatus";

// Support both MySQL (local) and PostgreSQL (production)
const isProduction = process.env.NODE_ENV === "production";
const databaseType = process.env.DATABASE_TYPE || (isProduction ? "postgres" : "mysql");

export const AppDataSource = new DataSource({
  type: databaseType as "mysql" | "postgres",
  host: process.env.DATABASE_HOST || process.env.MYSQL_HOST,
  port: parseInt(process.env.DATABASE_PORT || process.env.MYSQL_PORT || (databaseType === "postgres" ? "5432" : "3306")),
  username: process.env.DATABASE_USER || process.env.MYSQL_USER,
  password: process.env.DATABASE_PASSWORD || process.env.MYSQL_PASSWORD,
  database: process.env.DATABASE_NAME,
  entities: [User, Chat, ChatMember, Message, UserRelationship, MessageStatus],
  synchronize: !isProduction, // Set to false in production
  ssl: isProduction ? { rejectUnauthorized: false } : false,
  // logging: process.env.NODE_ENV !== "production",
});
