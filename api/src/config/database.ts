import { DataSource } from "typeorm";
import { User } from "../models/User";
import { Chat } from "../models/Chat";
import { ChatMember } from "../models/ChatMember";
import { Message } from "../models/Message";
import { UserRelationship } from "../models/UserRelationship";
import { MessageStatus } from "../models/MessageStatus";

const isProduction = process.env.NODE_ENV === "production";

// For Fly.io, use DATABASE_URL if available (PostgreSQL), otherwise use individual config (MySQL for local)
const databaseUrl = process.env.DATABASE_URL;

export const AppDataSource = new DataSource(
  databaseUrl
    ? {
        // Fly.io PostgreSQL configuration
        type: "postgres",
        url: databaseUrl,
        entities: [User, Chat, ChatMember, Message, UserRelationship, MessageStatus],
        synchronize: !isProduction,
        ssl: isProduction ? { rejectUnauthorized: false } : false,
        logging: !isProduction,
      }
    : {
        // Local MySQL configuration
        type: "mysql",
        host: process.env.MYSQL_HOST || "localhost",
        port: parseInt(process.env.MYSQL_PORT || "3306"),
        username: process.env.MYSQL_USER || "root",
        password: process.env.MYSQL_PASSWORD || "",
        database: process.env.DATABASE_NAME || "simple_telegram",
        entities: [User, Chat, ChatMember, Message, UserRelationship, MessageStatus],
        synchronize: !isProduction,
        logging: !isProduction,
      }
);
