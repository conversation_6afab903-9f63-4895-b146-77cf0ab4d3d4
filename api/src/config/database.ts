import { DataSource } from "typeorm";
import { User } from "../models/User";
import { Chat } from "../models/Chat";
import { ChatMember } from "../models/ChatMember";
import { Message } from "../models/Message";
import { UserRelationship } from "../models/UserRelationship";
import { MessageStatus } from "../models/MessageStatus";
import { dbConfig, appConfig } from "./env";

const isProduction = appConfig.nodeEnv === "production";

export const AppDataSource = new DataSource({
  type: dbConfig.type,
  host: dbConfig.host,
  port: dbConfig.port,
  username: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  entities: [User, Chat, ChatMember, Message, UserRelationship, MessageStatus],
  synchronize: !isProduction, // Set to false in production
  ssl: isProduction && dbConfig.type === "postgres" ? { rejectUnauthorized: false } : false,
  logging: appConfig.nodeEnv === "development",
  // Connection pool settings for better performance
  extra: {
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
  },
});
