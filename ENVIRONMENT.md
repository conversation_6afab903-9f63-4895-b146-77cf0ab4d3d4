# Environment Configuration Guide

This project uses a comprehensive environment management system with proper validation and security features.

## 📁 Environment Files

- **`.env.example`** - Template with all available variables
- **`.env.development`** - Development environment settings
- **`.env.production`** - Production environment template
- **`.env`** - Active environment file (created from templates)

## 🚀 Quick Setup

### Development Environment
```bash
./setup-env.sh dev
```

### Production Environment
```bash
./setup-env.sh prod
```

### Custom Environment
```bash
./setup-env.sh custom
# Then edit .env file manually
```

### Validate Configuration
```bash
./setup-env.sh validate
```

## 📋 Environment Variables

### Required Variables
- `MY<PERSON><PERSON>_PASSWORD` - Database password
- `JWT_SECRET` - JWT signing secret
- `DATABASE_NAME` - Database name

### Application Settings
- `NODE_ENV` - Environment (development/production)
- `PORT` - Application port (default: 5555)
- `LOG_LEVEL` - Logging level (debug/info/warn/error)

### Database Configuration
- `MYSQL_HOST` - Database host (default: mysqlDatabase)
- `MYSQL_PORT` - Database port (default: 3306)
- `MYSQL_USER` - Database user (default: root)
- `DATABASE_TYPE` - Database type (mysql/postgres)

### Security Settings
- `CORS_ORIGIN` - CORS allowed origins
- `RATE_LIMIT_WINDOW_MS` - Rate limiting window
- `RATE_LIMIT_MAX_REQUESTS` - Max requests per window

## 🔧 Configuration Features

### Environment Detection
The application automatically detects the environment and loads appropriate settings:

```typescript
import { config, appConfig, dbConfig } from './config/env';

// Access configuration
console.log(appConfig.nodeEnv);  // development/production
console.log(dbConfig.host);      // Database host
```

### Validation
Environment variables are validated on application startup:
- Required variables must be present
- Production passwords must meet minimum length requirements
- JWT secrets must be sufficiently long

### Type Safety
All environment variables are typed and have defaults:

```typescript
export const config = {
  app: {
    port: parseInt(process.env.PORT || "5555", 10),
    nodeEnv: process.env.NODE_ENV || "development",
  },
  // ... more config
};
```

## 🛡️ Security Best Practices

### Development
- Uses default passwords for convenience
- Enables debug logging
- Allows all CORS origins

### Production
- Generates strong random passwords
- Restricts CORS origins
- Disables debug features
- Validates password strength

## 🔄 Docker Compose Integration

### Development
```yaml
services:
  api:
    env_file:
      - .env.development
    environment:
      - NODE_ENV=${NODE_ENV:-development}
```

### Production
```yaml
services:
  api:
    env_file:
      - .env
    # Environment variables loaded from .env file
```

## 📝 Manual Configuration

If you prefer manual setup:

1. **Copy template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit variables:**
   ```bash
   nano .env
   ```

3. **Generate secure passwords:**
   ```bash
   # MySQL password
   openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
   
   # JWT secret
   openssl rand -base64 64 | tr -d "\n"
   ```

4. **Validate:**
   ```bash
   ./setup-env.sh validate
   ```

## 🚨 Troubleshooting

### Missing Environment Variables
```
Error: Missing required environment variables: JWT_SECRET, MYSQL_PASSWORD
```
**Solution:** Run `./setup-env.sh validate` to see missing variables.

### Weak Passwords in Production
```
Error: JWT_SECRET must be at least 32 characters long in production
```
**Solution:** Generate stronger passwords using the setup script.

### Database Connection Issues
```
Error: Database connection failed
```
**Solution:** Check database configuration in .env file.

## 📊 Environment Comparison

| Feature | Development | Production |
|---------|-------------|------------|
| Password Security | Default | Generated |
| Logging | Debug | Info |
| CORS | Permissive | Restricted |
| Database Sync | Enabled | Disabled |
| SSL | Disabled | Enabled |

## 🔗 Related Files

- `api/src/config/env.ts` - Environment configuration module
- `api/src/config/database.ts` - Database configuration
- `docker-compose.yml` - Development Docker setup
- `docker-compose.prod.yml` - Production Docker setup
- `setup-env.sh` - Environment setup script
