services:
  api:
    build: ./api
    restart: always
    ports:
      - "${PORT:-5555}:${PORT:-5555}"
    depends_on:
      mysqlDatabase:
        condition: service_healthy
    env_file:
      - .env
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-5555}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mysqlDatabase:
    image: mysql:8.0
    restart: always
    env_file:
      - .env
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=${DATABASE_NAME}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MY<PERSON>QL_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - app-network

  # Uncomment for database management in production (not recommended for security)
  # adminer:
  #   image: adminer
  #   restart: always
  #   ports:
  #     - "8888:8080"
  #   depends_on:
  #     - mysqlDatabase
  #   networks:
  #     - app-network

volumes:
  mysql_data:

networks:
  app-network:
    driver: bridge
