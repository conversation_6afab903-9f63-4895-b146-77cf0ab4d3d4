# fly.toml app configuration file generated for stbackend on 2024-01-01T00:00:00Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "stbackend"
primary_region = "iad"

[build]
  dockerfile = "./api/Dockerfile"

[env]
  NODE_ENV = "production"
  DATABASE_TYPE = "postgres"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[http_service.checks]]
  interval = "15s"
  timeout = "2s"
  grace_period = "5s"
  method = "GET"
  path = "/health"

[[vm]]
  memory = "256mb"
  cpu_kind = "shared"
  cpus = 1

[deploy]
  release_command = "npm run build"
