# fly.toml app configuration file for STBackend API
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.

app = "stbackend-api"
primary_region = "iad"

[build]
  dockerfile = "./Dockerfile"

[env]
  NODE_ENV = "production"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

  [[http_service.checks]]
    interval = "30s"
    timeout = "5s"
    grace_period = "10s"
    method = "GET"
    path = "/health"

[vm]
  memory = "512mb"
  cpu_kind = "shared"
  cpus = 1


