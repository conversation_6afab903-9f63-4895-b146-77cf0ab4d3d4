services:
  api:
    build: ./api
    restart: always
    ports:
      - "${PORT:-5555}:${PORT:-5555}"
    depends_on:
      - mysqlDatabase
    volumes:
      - ./api:/app
      - /app/node_modules
    env_file:
      - .env.development
    environment:
      - NODE_ENV=${NODE_ENV:-development}
  mysqlDatabase:
    image: mysql:8.0
    restart: always
    env_file:
      - .env.development
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=${DATABASE_NAME}
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
  adminer:
    image: adminer
    restart: always
    ports:
      - "8888:8080"
    depends_on:
      - mysqlDatabase
volumes:
  mysql_data:
