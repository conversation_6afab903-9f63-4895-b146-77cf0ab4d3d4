{"info": {"name": "STBackend API - Fly.io", "description": "Complete API collection for STBackend deployed on Fly.io", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "https://stbackend-api.fly.dev", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "Health & Info", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "App Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/info", "host": ["{{baseUrl}}"], "path": ["info"]}}}]}, {"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "        console.log('<PERSON><PERSON> saved:', response.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"login\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "        console.log('<PERSON><PERSON> saved:', response.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"login\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}}]}, {"name": "Chats", "item": [{"name": "Create Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Chat Room\",\n  \"description\": \"A test chat room for API testing\"\n}"}, "url": {"raw": "{{baseUrl}}/api/chats", "host": ["{{baseUrl}}"], "path": ["api", "chats"]}}}, {"name": "Get User Chats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/chats", "host": ["{{baseUrl}}"], "path": ["api", "chats"]}}}, {"name": "Get Chat by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/chats/1", "host": ["{{baseUrl}}"], "path": ["api", "chats", "1"]}}}]}, {"name": "Messages", "item": [{"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"chatId\": 1,\n  \"content\": \"Hello, this is a test message!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/messages", "host": ["{{baseUrl}}"], "path": ["api", "messages"]}}}, {"name": "Get Chat Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/chat/1", "host": ["{{baseUrl}}"], "path": ["api", "messages", "chat", "1"]}}}]}, {"name": "Relationships", "item": [{"name": "Send Friend Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"targetUserId\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/relationships/request", "host": ["{{baseUrl}}"], "path": ["api", "relationships", "request"]}}}, {"name": "Get User Relationships", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/relationships", "host": ["{{baseUrl}}"], "path": ["api", "relationships"]}}}]}]}