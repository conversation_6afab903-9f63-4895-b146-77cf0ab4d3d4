#!/bin/bash

# Fly.io Deployment Script for STBackend
# This script automates the complete deployment process to Fly.io

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}   Fly.io Deployment Script    ${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if flyctl is installed
check_flyctl() {
    if ! command -v flyctl &> /dev/null; then
        print_error "flyctl is not installed or not in PATH"
        print_status "Installing flyctl..."
        curl -L https://fly.io/install.sh | sh
        export PATH="/home/<USER>/.fly/bin:$PATH"
        print_status "Please add flyctl to your PATH: export PATH=\"/home/<USER>/.fly/bin:\$PATH\""
        return 1
    fi
    return 0
}

# Check if user is logged in
check_auth() {
    if ! flyctl auth whoami &> /dev/null; then
        print_warning "Not logged in to Fly.io"
        print_status "Please log in to Fly.io..."
        flyctl auth login
    else
        print_status "Already logged in to Fly.io"
    fi
}

# Create or update app
setup_app() {
    local app_name="stbackend-api"
    
    print_status "Setting up Fly.io app..."
    
    if flyctl apps list | grep -q "$app_name"; then
        print_status "App '$app_name' already exists"
    else
        print_status "Creating new app '$app_name'..."
        flyctl apps create "$app_name" --org personal
    fi
    
    # Update fly.toml with correct app name
    sed -i "s/app = \".*\"/app = \"$app_name\"/" fly.toml
    print_status "Updated fly.toml with app name: $app_name"
}

# Setup PostgreSQL database
setup_database() {
    local db_name="stbackend-db"
    local app_name="stbackend-api"
    
    print_status "Setting up PostgreSQL database..."
    
    # Check if database already exists
    if flyctl postgres list | grep -q "$db_name"; then
        print_status "Database '$db_name' already exists"
    else
        print_status "Creating PostgreSQL database '$db_name'..."
        flyctl postgres create --name "$db_name" --region iad --vm-size shared-cpu-1x --volume-size 1
    fi
    
    # Attach database to app
    print_status "Attaching database to app..."
    flyctl postgres attach "$db_name" --app "$app_name" || print_warning "Database might already be attached"
}

# Set environment variables
setup_secrets() {
    local app_name="stbackend-api"
    
    print_status "Setting up environment variables..."
    
    # Generate JWT secret if not provided
    if [ -z "$JWT_SECRET" ]; then
        JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
        print_status "Generated JWT secret"
    fi
    
    # Set secrets
    flyctl secrets set \
        JWT_SECRET="$JWT_SECRET" \
        NODE_ENV="production" \
        --app "$app_name"
    
    print_status "Environment variables set successfully"
}

# Deploy application
deploy_app() {
    local app_name="stbackend-api"
    
    print_status "Deploying application to Fly.io..."
    
    # Deploy
    flyctl deploy --app "$app_name"
    
    if [ $? -eq 0 ]; then
        print_status "✅ Deployment successful!"
        
        # Get app URL
        local app_url="https://$app_name.fly.dev"
        print_status "Your app is available at: $app_url"
        print_status "Health check: $app_url/health"
        print_status "API info: $app_url/info"
        
        # Test health endpoint
        print_status "Testing health endpoint..."
        if curl -f "$app_url/health" > /dev/null 2>&1; then
            print_status "✅ Health check passed!"
        else
            print_warning "⚠️  Health check failed. Check logs with: flyctl logs --app $app_name"
        fi
    else
        print_error "❌ Deployment failed!"
        return 1
    fi
}

# Show useful commands
show_commands() {
    local app_name="stbackend-api"
    
    echo ""
    print_status "Useful Fly.io commands:"
    echo "  flyctl logs --app $app_name                 # View logs"
    echo "  flyctl ssh console --app $app_name          # SSH into container"
    echo "  flyctl status --app $app_name               # Check app status"
    echo "  flyctl scale count 1 --app $app_name        # Scale to 1 instance"
    echo "  flyctl scale count 0 --app $app_name        # Scale to 0 (stop)"
    echo "  flyctl postgres connect --app stbackend-db  # Connect to database"
    echo ""
    print_status "To update your app:"
    echo "  git add . && git commit -m 'Update' && ./deploy-to-flyio.sh"
}

# Main deployment flow
main() {
    print_header
    
    # Set PATH for flyctl
    export PATH="/home/<USER>/.fly/bin:$PATH"
    
    print_status "Starting Fly.io deployment process..."
    
    # Check prerequisites
    if ! check_flyctl; then
        print_error "Please install flyctl and add it to PATH, then run this script again"
        exit 1
    fi
    
    check_auth
    setup_app
    setup_database
    setup_secrets
    deploy_app
    show_commands
    
    print_status "🎉 Deployment process completed!"
}

# Handle command line arguments
case "${1:-}" in
    "logs")
        export PATH="/home/<USER>/.fly/bin:$PATH"
        flyctl logs --app stbackend-api
        ;;
    "status")
        export PATH="/home/<USER>/.fly/bin:$PATH"
        flyctl status --app stbackend-api
        ;;
    "ssh")
        export PATH="/home/<USER>/.fly/bin:$PATH"
        flyctl ssh console --app stbackend-api
        ;;
    "scale")
        export PATH="/home/<USER>/.fly/bin:$PATH"
        flyctl scale count ${2:-1} --app stbackend-api
        ;;
    *)
        main
        ;;
esac
