FROM node:18-alpine

WORKDIR /app

# Install dependencies first (for better caching)
COPY api/package*.json ./
COPY api/tsconfig.json ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY api/src ./src

# For now, skip TypeScript compilation and run with ts-node
# RUN npm run build

# Don't remove dev dependencies since we need ts-node
# RUN npm ci --only=production && npm cache clean --force

# Create logs directory
RUN mkdir -p logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port (Fly.io uses PORT env var, defaults to 8080)
EXPOSE 8080

# Start the application with ts-node
CMD ["npm", "run", "dev"]
