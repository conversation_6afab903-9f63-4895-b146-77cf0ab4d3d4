# Deployment Guide for Fly.io

This guide will help you deploy your STBackend API to Fly.io on the free plan.

## Prerequisites

1. **Fly.io Account**: Sign up at https://fly.io/
2. **Fly CLI**: Already installed in this environment

## Step-by-Step Deployment

### 1. Authenticate with Fly.io

```bash
export PATH="/home/<USER>/.fly/bin:$PATH"
flyctl auth login
```

This will open a browser window for you to log in to your Fly.io account.

### 2. Initialize the Fly.io App

```bash
flyctl apps create stbackend
```

Note: If "stbackend" is taken, choose a different name and update the `app` field in `fly.toml`.

### 3. Create PostgreSQL Database

Fly.io free tier includes PostgreSQL. Create a database:

```bash
flyctl postgres create --name stbackend-db --region iad
```

### 4. Attach Database to Your App

```bash
flyctl postgres attach --app stbackend stbackend-db
```

This will automatically set the `DATABASE_URL` environment variable.

### 5. Set Environment Variables

```bash
flyctl secrets set JWT_SECRET="your-super-secret-jwt-key-here" --app stbackend
flyctl secrets set NODE_ENV="production" --app stbackend
```

### 6. Deploy the Application

```bash
flyctl deploy
```

### 7. Check Deployment Status

```bash
flyctl status --app stbackend
flyctl logs --app stbackend
```

## Configuration Details

### Database Configuration

The app is configured to automatically detect the database type:
- **Local Development**: Uses MySQL (from docker-compose.yml)
- **Production (Fly.io)**: Uses PostgreSQL

### Environment Variables

The following environment variables are automatically configured:
- `DATABASE_URL`: Set by Fly.io when you attach the PostgreSQL database
- `NODE_ENV`: Set to "production"
- `DATABASE_TYPE`: Set to "postgres" in fly.toml
- `PORT`: Set to "8080" in fly.toml

You need to manually set:
- `JWT_SECRET`: Your JWT signing secret

### Health Check

The app includes a health check endpoint at `/health` that Fly.io uses to monitor your application.

## Accessing Your Deployed App

After successful deployment, your API will be available at:
```
https://stbackend.fly.dev
```

Test the health endpoint:
```
curl https://stbackend.fly.dev/health
```

Test the API:
```
curl https://stbackend.fly.dev/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","login":"<EMAIL>","password":"password123"}'
```

## Troubleshooting

### Check Logs
```bash
flyctl logs --app stbackend
```

### SSH into the Container
```bash
flyctl ssh console --app stbackend
```

### Scale Down to Save Resources
```bash
flyctl scale count 0 --app stbackend  # Stop all instances
flyctl scale count 1 --app stbackend  # Start one instance
```

## Free Tier Limitations

- **Shared CPU**: 1 shared CPU
- **Memory**: 256MB RAM
- **Storage**: 3GB persistent volume for PostgreSQL
- **Bandwidth**: 160GB/month outbound
- **Auto-sleep**: Apps sleep after inactivity (auto-wake on request)

## Next Steps

1. Set up a custom domain (optional)
2. Configure CI/CD for automatic deployments
3. Set up monitoring and alerts
4. Consider upgrading to paid plan for production use

## Support

- Fly.io Documentation: https://fly.io/docs/
- Fly.io Community: https://community.fly.io/
