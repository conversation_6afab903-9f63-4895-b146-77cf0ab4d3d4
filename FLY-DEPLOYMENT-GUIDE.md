# Complete Fly.io Deployment Guide

This guide will walk you through deploying your STBackend project (Express + MySQL/PostgreSQL) to Fly.io from start to finish.

## 🚀 Quick Start (Automated)

```bash
# One-command deployment
./deploy-to-flyio.sh
```

## 📋 Manual Step-by-Step Process

### Step 1: Prerequisites

1. **Fly.io Account**: Sign up at https://fly.io/
2. **Install Fly CLI**:
   ```bash
   curl -L https://fly.io/install.sh | sh
   export PATH="/home/<USER>/.fly/bin:$PATH"
   ```

### Step 2: Authentication

```bash
flyctl auth login
```

### Step 3: Create Application

```bash
flyctl apps create stbackend-api
```

### Step 4: Setup PostgreSQL Database

```bash
# Create PostgreSQL database (free tier)
flyctl postgres create --name stbackend-db --region iad --vm-size shared-cpu-1x --volume-size 1

# Attach database to your app
flyctl postgres attach stbackend-db --app stbackend-api
```

### Step 5: Set Environment Variables

```bash
# Generate JWT secret
JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')

# Set secrets
flyctl secrets set \
  JWT_SECRET="$JWT_SECRET" \
  NODE_ENV="production" \
  --app stbackend-api
```

### Step 6: Deploy

```bash
flyctl deploy --app stbackend-api
```

## 🔧 Configuration Files

### fly.toml
```toml
app = "stbackend-api"
primary_region = "iad"

[build]
  dockerfile = "./api/Dockerfile"

[env]
  NODE_ENV = "production"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0

  [[http_service.checks]]
    interval = "30s"
    timeout = "5s"
    grace_period = "10s"
    method = "GET"
    path = "/health"

[vm]
  memory = "512mb"
  cpu_kind = "shared"
  cpus = 1
```

### Dockerfile (api/Dockerfile)
- Production-ready Node.js 18 Alpine image
- Multi-stage build for optimization
- Non-root user for security
- Health checks included

## 🗄️ Database Migration

### From MySQL to PostgreSQL

Your app automatically detects the database type:

- **Local Development**: Uses MySQL (docker-compose)
- **Fly.io Production**: Uses PostgreSQL (DATABASE_URL)

The database configuration in `api/src/config/database.ts` handles both:

```typescript
const databaseUrl = process.env.DATABASE_URL;

export const AppDataSource = new DataSource(
  databaseUrl
    ? {
        // Fly.io PostgreSQL
        type: "postgres",
        url: databaseUrl,
        // ...
      }
    : {
        // Local MySQL
        type: "mysql",
        host: process.env.MYSQL_HOST,
        // ...
      }
);
```

## 🌐 Accessing Your App

After deployment:

- **Main URL**: `https://stbackend-api.fly.dev`
- **Health Check**: `https://stbackend-api.fly.dev/health`
- **API Info**: `https://stbackend-api.fly.dev/info`

## 📊 Monitoring & Management

### View Logs
```bash
flyctl logs --app stbackend-api
```

### Check Status
```bash
flyctl status --app stbackend-api
```

### SSH into Container
```bash
flyctl ssh console --app stbackend-api
```

### Scale Application
```bash
# Scale up
flyctl scale count 1 --app stbackend-api

# Scale down (stop)
flyctl scale count 0 --app stbackend-api
```

### Database Management
```bash
# Connect to PostgreSQL
flyctl postgres connect --app stbackend-db

# Database proxy (for external tools)
flyctl proxy 5432 --app stbackend-db
```

## 💰 Cost Optimization (Free Tier)

### Free Tier Includes:
- **Compute**: 3 shared-cpu-1x VMs (2,340 hours/month)
- **RAM**: 256MB per VM
- **Storage**: 3GB persistent volumes
- **Bandwidth**: 160GB outbound/month
- **PostgreSQL**: 3GB storage, shared CPU

### Auto-scaling Configuration:
```toml
[http_service]
  auto_stop_machines = true    # Stop when idle
  auto_start_machines = true   # Start on request
  min_machines_running = 0     # No always-on instances
```

## 🔄 CI/CD Integration

### GitHub Actions Example:
```yaml
name: Deploy to Fly.io
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - run: flyctl deploy --remote-only
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
```

## 🛠️ Troubleshooting

### Common Issues:

1. **Build Failures**:
   ```bash
   # Check build logs
   flyctl logs --app stbackend-api
   ```

2. **Database Connection Issues**:
   ```bash
   # Verify DATABASE_URL is set
   flyctl secrets list --app stbackend-api
   ```

3. **Health Check Failures**:
   ```bash
   # Test health endpoint locally
   curl https://stbackend-api.fly.dev/health
   ```

4. **Memory Issues**:
   ```bash
   # Increase memory
   flyctl scale memory 1024 --app stbackend-api
   ```

## 🔐 Security Best Practices

1. **Environment Variables**: All secrets stored in Fly.io secrets
2. **HTTPS**: Forced HTTPS with automatic certificates
3. **Non-root User**: Docker container runs as non-root
4. **Health Checks**: Automatic health monitoring
5. **Auto-scaling**: Scales to zero when not in use

## 📈 Performance Optimization

1. **Multi-stage Docker Build**: Smaller image size
2. **Connection Pooling**: Database connection optimization
3. **Health Checks**: Fast application recovery
4. **Regional Deployment**: Choose region closest to users

## 🔄 Updates and Maintenance

### Update Application:
```bash
# Make changes to your code
git add .
git commit -m "Update application"

# Deploy updates
./deploy-to-flyio.sh
```

### Backup Database:
```bash
# Create backup
flyctl postgres connect --app stbackend-db
pg_dump > backup.sql
```

## 📞 Support

- **Fly.io Docs**: https://fly.io/docs/
- **Community**: https://community.fly.io/
- **Status**: https://status.fly.io/
