#!/bin/bash

# Environment Setup Script
# This script helps you set up environment variables for different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  STBackend Environment Setup  ${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

generate_jwt_secret() {
    openssl rand -base64 64 | tr -d "\n"
}

setup_development() {
    print_status "Setting up development environment..."
    
    if [ ! -f ".env.development" ]; then
        print_error ".env.development template not found!"
        exit 1
    fi
    
    cp .env.development .env
    print_status "✅ Copied .env.development to .env"
    print_warning "Development environment uses default passwords. Change them if needed."
}

setup_production() {
    print_status "Setting up production environment..."
    
    if [ ! -f ".env.production" ]; then
        print_error ".env.production template not found!"
        exit 1
    fi
    
    # Generate secure passwords
    MYSQL_PASSWORD=$(generate_password)
    JWT_SECRET=$(generate_jwt_secret)
    
    # Copy template and replace placeholders
    cp .env.production .env
    
    # Replace placeholders with generated values
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your_super_secure_mysql_password_here/$MYSQL_PASSWORD/g" .env
        sed -i '' "s/your_super_secure_jwt_secret_here/$JWT_SECRET/g" .env
    else
        # Linux
        sed -i "s/your_super_secure_mysql_password_here/$MYSQL_PASSWORD/g" .env
        sed -i "s/your_super_secure_jwt_secret_here/$JWT_SECRET/g" .env
    fi
    
    print_status "✅ Created .env with secure generated passwords"
    echo ""
    print_warning "Generated credentials:"
    echo "  MySQL Password: $MYSQL_PASSWORD"
    echo "  JWT Secret: $JWT_SECRET"
    echo ""
    print_warning "Please save these credentials securely!"
}

setup_custom() {
    print_status "Setting up custom environment..."
    
    if [ ! -f ".env.example" ]; then
        print_error ".env.example template not found!"
        exit 1
    fi
    
    cp .env.example .env
    print_status "✅ Copied .env.example to .env"
    print_warning "Please edit .env file and fill in your custom values."
}

validate_env() {
    print_status "Validating environment configuration..."
    
    if [ ! -f ".env" ]; then
        print_error ".env file not found!"
        return 1
    fi
    
    # Check required variables
    source .env
    
    REQUIRED_VARS=("MYSQL_PASSWORD" "JWT_SECRET" "DATABASE_NAME")
    MISSING_VARS=()
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            MISSING_VARS+=("$var")
        fi
    done
    
    if [ ${#MISSING_VARS[@]} -ne 0 ]; then
        print_error "Missing required environment variables:"
        for var in "${MISSING_VARS[@]}"; do
            echo "  - $var"
        done
        return 1
    fi
    
    # Check password strength in production
    if [ "$NODE_ENV" = "production" ]; then
        if [ ${#MYSQL_PASSWORD} -lt 8 ]; then
            print_warning "MySQL password should be at least 8 characters for production"
        fi
        
        if [ ${#JWT_SECRET} -lt 32 ]; then
            print_warning "JWT secret should be at least 32 characters for production"
        fi
    fi
    
    print_status "✅ Environment validation passed"
}

print_header

case "${1:-}" in
    "dev"|"development")
        setup_development
        ;;
    "prod"|"production")
        setup_production
        ;;
    "custom")
        setup_custom
        ;;
    "validate")
        validate_env
        exit $?
        ;;
    *)
        echo "Usage: $0 {dev|prod|custom|validate}"
        echo ""
        echo "Commands:"
        echo "  dev        - Setup development environment"
        echo "  prod       - Setup production environment with generated secrets"
        echo "  custom     - Setup custom environment (manual configuration)"
        echo "  validate   - Validate current .env file"
        echo ""
        echo "Examples:"
        echo "  $0 dev      # For local development"
        echo "  $0 prod     # For production deployment"
        echo "  $0 validate # Check current configuration"
        exit 1
        ;;
esac

echo ""
print_status "Environment setup completed!"
print_status "Next steps:"
echo "  1. Review your .env file"
echo "  2. Run: $0 validate"
echo "  3. Start your application: docker-compose up -d"
