# Environment Variables Template
# Copy this file to .env and fill in your values

# Database Configuration
MYSQL_HOST=mysqlDatabase
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password_here
MYSQL_ROOT_PASSWORD=your_mysql_root_password_here
DATABASE_NAME=simple_telegram

# JWT Secret (generate with: openssl rand -base64 64)
JWT_SECRET=your_jwt_secret_here

# Application Settings
NODE_ENV=development
PORT=5555
LOG_LEVEL=debug

# Security Settings (for production)
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Optional: Debug settings
DEBUG=app:*
