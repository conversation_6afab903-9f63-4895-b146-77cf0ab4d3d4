<!DOCTYPE html>
<html>
<head>
    <title>STBackend Database Access</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .option { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .credentials { background: #e8f4fd; padding: 15px; border-radius: 5px; font-family: monospace; }
        .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px 0 0; }
        .button:hover { background: #005a87; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ STBackend Database Access</h1>
        
        <div class="warning">
            <strong>⚠️ Security Note:</strong> These credentials are for development/testing only. 
            Change them in production!
        </div>

        <div class="option">
            <h2>📊 Database Connection Details</h2>
            <div class="credentials">
                <strong>Database Type:</strong> PostgreSQL<br>
                <strong>Host:</strong> stbackend-db.flycast (internal) or localhost (via proxy)<br>
                <strong>Port:</strong> 5432<br>
                <strong>Database:</strong> stbackend_api<br>
                <strong>Username:</strong> stbackend_api<br>
                <strong>Password:</strong> LX5gqMHCNItG8Rf<br>
                <strong>SSL:</strong> Required
            </div>
        </div>

        <div class="option">
            <h2>🌐 Online Database Tools</h2>
            <p>Use these web-based tools to manage your database:</p>
            
            <a href="https://www.adminer.org/" class="button" target="_blank">
                📋 Adminer (Download)
            </a>
            
            <a href="https://www.pgadmin.org/download/" class="button" target="_blank">
                🐘 pgAdmin (Download)
            </a>
            
            <a href="https://dbgate.org/" class="button" target="_blank">
                🚀 DBGate (Modern UI)
            </a>
            
            <a href="https://dbeaver.io/download/" class="button" target="_blank">
                🔧 DBeaver (Desktop)
            </a>
        </div>

        <div class="option">
            <h2>🔗 Fly.io Proxy Setup</h2>
            <p>To connect from your local machine:</p>
            <div class="credentials">
# Start proxy in terminal:<br>
export PATH="/home/<USER>/.fly/bin:$PATH"<br>
flyctl proxy 5432 --app stbackend-db<br><br>

# Then connect to localhost:5432 with the credentials above
            </div>
        </div>

        <div class="option">
            <h2>💻 Command Line Access</h2>
            <p>Direct PostgreSQL command line:</p>
            <div class="credentials">
# Connect via Fly.io console:<br>
flyctl postgres connect --app stbackend-db<br><br>

# Or use psql with proxy:<br>
psql -h localhost -p 5432 -U stbackend_api -d stbackend_api
            </div>
        </div>

        <div class="option">
            <h2>🔍 Quick Database Check</h2>
            <p>Your API endpoints for testing:</p>
            <div class="credentials">
<strong>API Base URL:</strong> https://stbackend-api.fly.dev<br>
<strong>Health Check:</strong> https://stbackend-api.fly.dev/health<br>
<strong>API Info:</strong> https://stbackend-api.fly.dev/info<br><br>

<strong>Test Registration:</strong><br>
curl -X POST https://stbackend-api.fly.dev/api/auth/register \<br>
  -H "Content-Type: application/json" \<br>
  -d '{"username":"test","login":"<EMAIL>","password":"password123"}'
            </div>
        </div>

        <div class="option">
            <h2>📱 Mobile/Tablet Access</h2>
            <p>For mobile database management:</p>
            
            <a href="https://apps.apple.com/app/postgres-client/id1460017676" class="button" target="_blank">
                📱 iOS: Postgres Client
            </a>
            
            <a href="https://play.google.com/store/apps/details?id=com.dbeaver.android" class="button" target="_blank">
                🤖 Android: DBeaver
            </a>
        </div>

        <div class="option">
            <h2>🛠️ Troubleshooting</h2>
            <p>If you can't connect:</p>
            <ul>
                <li>Make sure the proxy is running: <code>flyctl proxy 5432 --app stbackend-db</code></li>
                <li>Check if the database is running: <code>flyctl status --app stbackend-db</code></li>
                <li>Verify credentials: <code>flyctl postgres connect --app stbackend-db</code></li>
                <li>Check logs: <code>flyctl logs --app stbackend-db</code></li>
            </ul>
        </div>
    </div>
</body>
</html>
